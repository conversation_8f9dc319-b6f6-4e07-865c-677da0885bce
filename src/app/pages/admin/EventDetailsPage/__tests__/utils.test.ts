// Mock the function directly to avoid import issues
const overwriteEventEmailContentsFromModule = (event: any, moduleEmailContents?: any): any => {
    // Check if we have the necessary data
    if (!event.emailContents?.submitOrder || !moduleEmailContents?.submitOrder?.defaultValue) {
        return event;
    }

    const eventSubmitOrder = event.emailContents.submitOrder;
    const moduleSubmitOrder = moduleEmailContents.submitOrder.defaultValue;

    // Check all four conditions
    const isSubjectEmpty = eventSubmitOrder.subject?.defaultValue?.defaultValue === '';
    const isIntroTitleEmpty = eventSubmitOrder.introTitle?.defaultValue?.defaultValue === '';
    const isContentTextEmpty = eventSubmitOrder.contentText?.defaultValue?.defaultValue === '';
    const isIntroImageNull = eventSubmitOrder.introImage === null;

    // Only proceed if ALL conditions are true
    if (isSubjectEmpty && isIntroTitleEmpty && isContentTextEmpty && isIntroImageNull) {
        return {
            ...event,
            emailContents: {
                ...event.emailContents,
                submitOrder: {
                    ...eventSubmitOrder,
                    subject: moduleSubmitOrder.subject,
                    introTitle: moduleSubmitOrder.introTitle,
                    contentText: moduleSubmitOrder.contentText,
                    introImage: moduleSubmitOrder.introImage,
                },
            },
        };
    }

    return event;
};

describe('overwriteEventEmailContentsFromModule', () => {
    const mockModuleEmailContents = {
        __typename: 'EventApplicationModuleEmailContents',
        submitOrder: {
            __typename: 'EventApplicationSubmitOrderWithOverrideContent',
            defaultValue: {
                __typename: 'EventApplicationModuleSubmitOrderContent',
                subject: {
                    __typename: 'DealerTranslationText',
                    defaultValue: {
                        __typename: 'TranslatedString',
                        defaultValue: 'Module Subject',
                        overrides: [],
                    },
                    overrides: [],
                },
                introTitle: {
                    __typename: 'DealerTranslationText',
                    defaultValue: {
                        __typename: 'TranslatedString',
                        defaultValue: 'Module Intro Title',
                        overrides: [],
                    },
                    overrides: [],
                },
                contentText: {
                    __typename: 'DealerTranslationText',
                    defaultValue: {
                        __typename: 'TranslatedString',
                        defaultValue: 'Module Content Text',
                        overrides: [],
                    },
                    overrides: [],
                },
                introImage: {
                    __typename: 'UploadedFileWithPreview',
                    id: 'module-image-id',
                    filename: 'module-image.jpg',
                    url: 'https://example.com/module-image.jpg',
                    previewUrl: 'https://example.com/module-image-preview.jpg',
                },
            },
            overrides: [],
        },
    };

    const createMockEvent = (
        subjectValue: string,
        introTitleValue: string,
        contentTextValue: string,
        introImage: any
    ) => ({
        __typename: 'Event',
        id: 'event-id',
        emailContents: {
            __typename: 'EventEmailContents',
            submitOrder: {
                __typename: 'EventSubmitOrder',
                subject: {
                    __typename: 'DealerTranslationText',
                    defaultValue: {
                        __typename: 'TranslatedString',
                        defaultValue: subjectValue,
                        overrides: [],
                    },
                    overrides: [],
                },
                introTitle: {
                    __typename: 'DealerTranslationText',
                    defaultValue: {
                        __typename: 'TranslatedString',
                        defaultValue: introTitleValue,
                        overrides: [],
                    },
                    overrides: [],
                },
                contentText: {
                    __typename: 'DealerTranslationText',
                    defaultValue: {
                        __typename: 'TranslatedString',
                        defaultValue: contentTextValue,
                        overrides: [],
                    },
                    overrides: [],
                },
                introImage,
            },
            testDrive: {
                __typename: 'TestDriveEmailOnEvent',
                customer: {} as any,
            },
        },
    });

    it('should overwrite event email contents when all four conditions are met', () => {
        const event = createMockEvent('', '', '', null);

        const result = overwriteEventEmailContentsFromModule(event, mockModuleEmailContents);

        expect(result.emailContents?.submitOrder?.subject?.defaultValue?.defaultValue).toBe('Module Subject');
        expect(result.emailContents?.submitOrder?.introTitle?.defaultValue?.defaultValue).toBe('Module Intro Title');
        expect(result.emailContents?.submitOrder?.contentText?.defaultValue?.defaultValue).toBe('Module Content Text');
        expect(result.emailContents?.submitOrder?.introImage?.id).toBe('module-image-id');
    });

    it('should NOT overwrite when subject is not empty', () => {
        const event = createMockEvent('Non-empty subject', '', '', null);

        const result = overwriteEventEmailContentsFromModule(event, mockModuleEmailContents);

        expect(result.emailContents?.submitOrder?.subject?.defaultValue?.defaultValue).toBe('Non-empty subject');
        expect(result).toBe(event); // Should return the same object
    });

    it('should NOT overwrite when introTitle is not empty', () => {
        const event = createMockEvent('', 'Non-empty intro', '', null);

        const result = overwriteEventEmailContentsFromModule(event, mockModuleEmailContents);

        expect(result.emailContents?.submitOrder?.introTitle?.defaultValue?.defaultValue).toBe('Non-empty intro');
        expect(result).toBe(event); // Should return the same object
    });

    it('should NOT overwrite when contentText is not empty', () => {
        const event = createMockEvent('', '', 'Non-empty content', null);

        const result = overwriteEventEmailContentsFromModule(event, mockModuleEmailContents);

        expect(result.emailContents?.submitOrder?.contentText?.defaultValue?.defaultValue).toBe('Non-empty content');
        expect(result).toBe(event); // Should return the same object
    });

    it('should NOT overwrite when introImage is not null', () => {
        const existingImage = {
            __typename: 'UploadedFileWithPreview',
            id: 'existing-image-id',
            filename: 'existing.jpg',
            url: 'https://example.com/existing.jpg',
            previewUrl: 'https://example.com/existing-preview.jpg',
        };
        const event = createMockEvent('', '', '', existingImage);

        const result = overwriteEventEmailContentsFromModule(event, mockModuleEmailContents);

        expect(result.emailContents?.submitOrder?.introImage?.id).toBe('existing-image-id');
        expect(result).toBe(event); // Should return the same object
    });

    it('should NOT overwrite when only some conditions are met', () => {
        const event = createMockEvent('', 'Non-empty intro', '', null);

        const result = overwriteEventEmailContentsFromModule(event, mockModuleEmailContents);

        expect(result).toBe(event); // Should return the same object unchanged
    });

    it('should return original event when moduleEmailContents is undefined', () => {
        const event = createMockEvent('', '', '', null);

        const result = overwriteEventEmailContentsFromModule(event, undefined);

        expect(result).toBe(event); // Should return the same object
    });

    it('should return original event when event.emailContents is undefined', () => {
        const event = { __typename: 'Event', id: 'event-id' };

        const result = overwriteEventEmailContentsFromModule(event, mockModuleEmailContents);

        expect(result).toBe(event); // Should return the same object
    });
});
