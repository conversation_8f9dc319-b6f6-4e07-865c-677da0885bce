/**
 * Example usage of overwriteEventEmailContentsFromModule function
 * 
 * This function conditionally overwrites event's emailContents.submitOrder properties 
 * with values from the related module when ALL four conditions are met simultaneously:
 * 
 * 1. event.emailContents.submitOrder.subject.defaultValue.defaultValue is an empty string ("")
 * 2. event.emailContents.submitOrder.introTitle.defaultValue.defaultValue is an empty string ("")
 * 3. event.emailContents.submitOrder.contentText.defaultValue.defaultValue is an empty string ("")
 * 4. event.emailContents.submitOrder.introImage is null
 */

import { overwriteEventEmailContentsFromModule } from './utils';
import type { EventDataFragment } from '../../../api/fragments/EventData';
import type { EventApplicationModuleEmailContentSpecsFragment } from '../../../api/fragments/EventApplicationModuleEmailContentSpecs';

// Example usage in a component or hook
export const useEventWithModuleDefaults = (
    event: EventDataFragment,
    moduleEmailContents?: EventApplicationModuleEmailContentSpecsFragment
) => {
    // Apply the conditional overwrite logic
    const eventWithDefaults = overwriteEventEmailContentsFromModule(event, moduleEmailContents);
    
    return eventWithDefaults;
};

// Example usage in a mutation or data processing function
export const processEventData = async (
    event: EventDataFragment,
    moduleEmailContents?: EventApplicationModuleEmailContentSpecsFragment
) => {
    // Apply conditional overwrite before processing
    const processedEvent = overwriteEventEmailContentsFromModule(event, moduleEmailContents);
    
    // Continue with other processing...
    return processedEvent;
};

// Example of how to fetch module email contents and apply the logic
export const applyModuleDefaultsToEvent = async (
    event: EventDataFragment,
    apolloClient: any // Replace with proper Apollo client type
) => {
    // If the event's module is an EventApplicationModule, fetch its email contents
    if (event.module?.__typename === 'EventApplicationModule') {
        try {
            // You would need to create a query to fetch the module with email contents
            // This is just an example of how you might structure it
            const { data } = await apolloClient.query({
                query: GET_MODULE_EMAIL_CONTENTS, // You would need to define this query
                variables: { moduleId: event.module.id },
            });
            
            const moduleEmailContents = data?.module?.emailContents;
            
            // Apply the conditional overwrite
            return overwriteEventEmailContentsFromModule(event, moduleEmailContents);
        } catch (error) {
            console.error('Failed to fetch module email contents:', error);
            return event; // Return original event if fetch fails
        }
    }
    
    return event; // Return original event if not an EventApplicationModule
};

/**
 * Example scenarios:
 * 
 * Scenario 1: All conditions met - OVERWRITE OCCURS
 * - event.emailContents.submitOrder.subject.defaultValue.defaultValue = ""
 * - event.emailContents.submitOrder.introTitle.defaultValue.defaultValue = ""
 * - event.emailContents.submitOrder.contentText.defaultValue.defaultValue = ""
 * - event.emailContents.submitOrder.introImage = null
 * Result: Event's submitOrder properties are replaced with module's defaults
 * 
 * Scenario 2: Some conditions met - NO OVERWRITE
 * - event.emailContents.submitOrder.subject.defaultValue.defaultValue = "Custom Subject"
 * - event.emailContents.submitOrder.introTitle.defaultValue.defaultValue = ""
 * - event.emailContents.submitOrder.contentText.defaultValue.defaultValue = ""
 * - event.emailContents.submitOrder.introImage = null
 * Result: Event remains unchanged (original object returned)
 * 
 * Scenario 3: No module email contents - NO OVERWRITE
 * - moduleEmailContents = undefined
 * Result: Event remains unchanged (original object returned)
 */
