import type { AnyBulkWriteOperation } from 'mongodb';
import type { Event, EventApplicationModule } from '../documents';
import type { DatabaseContext } from '../getDatabaseContext';

// obtained from t('emails:event.submitOrder')
// as previously defined as initial value for new LCF
const incorrectDefaults = {
    subject: '{{companyName}}: {{submissionType}} Submitted',
    introTitle: '{{variantName}}',
    contentText:
        // eslint-disable-next-line max-len
        "Congratulations on the purchase of your new {{companyName}} {{modelName}}. You're about to embark on a journey fuelled by sheer exhilaration and thrilling adventures. Get ready to experience what {{companyName}} enthusiasts around the world describe as a truly transformative moment.",
};

export default {
    identifier: '317_fixEventEmailContentTranslationKeys',

    async up({ regular: { db } }: DatabaseContext): Promise<void> {
        const problematicEvents = await db
            .collection<Event>('events')
            .find({
                'emailContents.submitOrder.subject.defaultValue.defaultValue': incorrectDefaults.subject,
                'emailContents.submitOrder.introTitle.defaultValue.defaultValue': incorrectDefaults.introTitle,
                'emailContents.submitOrder.contentText.defaultValue.defaultValue': incorrectDefaults.contentText,
                $or: [
                    { 'emailContents.submitOrder.introImage': { $exists: false } },
                    { 'emailContents.submitOrder.introImage': { $eq: null } },
                ],
            })
            .toArray();

        if (problematicEvents.length === 0) {
            console.warn('No events need to be updated. Migration completed.');

            return;
        }

        // Get unique module IDs from problematic events
        const uniqueModuleIds = [...new Set(problematicEvents.map(event => event.moduleId))];

        // Fetch all modules in one batch
        const modules = await db
            .collection<EventApplicationModule>('modules')
            .find({ _id: { $in: uniqueModuleIds } })
            .toArray();

        // Create a map for quick module lookup
        const moduleMap = new Map(modules.map(module => [module._id.toString(), module]));

        // Prepare bulk write operations
        const bulkWriteOps: AnyBulkWriteOperation[] = [];

        for (const event of problematicEvents) {
            const module = moduleMap.get(event.moduleId.toString());

            if (!module) {
                console.warn(`  - Warning: Module not found for event ${event._id}, skipping`);
                continue;
            }

            if (!module.emailContents?.submitOrder) {
                console.warn(`  - Warning: Module ${module._id} has no submitOrder email contents, skipping`);
                continue;
            }

            const moduleEmailContents = module.emailContents.submitOrder.defaultValue;

            bulkWriteOps.push({
                updateOne: {
                    filter: { _id: event._id },
                    update: {
                        $set: {
                            'emailContents.submitOrder.subject.defaultValue.defaultValue':
                                moduleEmailContents.subject.defaultValue.defaultValue,
                            'emailContents.submitOrder.introTitle.defaultValue.defaultValue':
                                moduleEmailContents.introTitle.defaultValue.defaultValue,
                            'emailContents.submitOrder.contentText.defaultValue.defaultValue':
                                moduleEmailContents.contentText.defaultValue.defaultValue,
                        },
                    },
                },
            });
        }

        if (bulkWriteOps.length > 0) {
            await db.collection('events').bulkWrite(bulkWriteOps);
        }

        // Verification: Check if any problematic events still exist
        const remainingProblematicEvents = await db.collection<Event>('events').countDocuments({
            'emailContents.submitOrder.subject.defaultValue.defaultValue': incorrectDefaults.subject,
            'emailContents.submitOrder.introTitle.defaultValue.defaultValue': incorrectDefaults.introTitle,
            'emailContents.submitOrder.contentText.defaultValue.defaultValue': incorrectDefaults.contentText,
            'emailContents.submitOrder.introImage': null,
        });

        if (remainingProblematicEvents > 0) {
            console.warn(`Warning: ${remainingProblematicEvents} events still have translation key values`);
        }
    },
};
